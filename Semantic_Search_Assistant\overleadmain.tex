\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage{geometry}
\usepackage{hyperref}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{enumitem}
\usepackage{longtable}

% Page setup
\geometry{margin=1in}

% Colors
\definecolor{codeblue}{RGB}{0,102,204}
\definecolor{backcolour}{RGB}{245,245,245}

% Code listing style
\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},
    basicstyle=\ttfamily\footnotesize,
    breaklines=true,
    keepspaces=true,
    showspaces=false,
    showstringspaces=false,
    tabsize=2
}
\lstset{style=mystyle}

\title{
    \textbf{Semantic Search Assistant} \\
    Real-time Document Search Desktop Application
}
\author{Project Documentation}
\date{\today}

\begin{document}

\maketitle

\section{Project Overview}

Real-time semantic search desktop application that monitors typing in any text editor and provides instant search results from indexed documents.

\subsection{Main Applications}
\begin{itemize}
    \item \textbf{enhanced\_global\_monitor.py}: Tkinter GUI with global keyboard monitoring
    \item \textbf{Electron app}: React-based desktop interface with Canvas feature
    \item \textbf{FastAPI backend}: Document processing and vector search
\end{itemize}

\section{Technology Stack}

\subsection{Python Dependencies (requirements.txt)}
\begin{itemize}
    \item lancedb>=0.3.0, sentence\_transformers>=2.2.0
    \item fastapi>=0.100.0, uvicorn[standard]>=0.23.0
    \item PyPDF2>=3.0.0, python-docx>=0.8.11
    \item keyboard>=0.13.5, pyperclip>=1.8.2
    \item watchdog>=3.0.0, requests, tkinter
\end{itemize}

\subsection{Electron Dependencies (package.json)}
\begin{itemize}
    \item electron: 27.0.0, react: 18.2.0
    \item framer-motion: 10.16.0, axios: 1.6.0
    \item tailwindcss, lucide-react
\end{itemize}

\section{Main Files}

\subsection{Python Applications}
\begin{itemize}
    \item \textbf{enhanced\_global\_monitor.py}: Tkinter GUI with keyboard monitoring, search display
    \item \textbf{api\_service.py}: FastAPI server (port 8000) with /search, /health endpoints
    \item \textbf{auto\_indexer.py}: Monitors test\_docs/, documents/, data/documents/ folders
    \item \textbf{database.py}: LanceDB vector store with sentence-transformers embeddings
\end{itemize}

\subsection{Electron App}
\begin{itemize}
    \item \textbf{main.js}: Creates main window and floating window
    \item \textbf{FloatingApp.js}: React UI with search, canvas, context tabs
    \item \textbf{Canvas.js}: Drag-drop workspace with pan/zoom
    \item \textbf{ApiService.js}: HTTP client for backend API calls
\end{itemize}

\section{How It Works}

\subsection{Enhanced Global Monitor (Tkinter App)}
\begin{enumerate}
    \item Run: python enhanced\_global\_monitor.py
    \item Click "Start Backend" - launches FastAPI server on port 8000
    \item Click "Start Global Monitor" - enables keyboard.on\_press() monitoring
    \item Type in any application (Word, Notepad, VS Code)
    \item Each keystroke builds query string, sends to /search endpoint
    \item Results displayed in tkinter Text widget
    \item Double-click any result to copy via pyperclip
    \item SPACEBAR clears current search
\end{enumerate}

\subsection{Auto Indexing Process}
\begin{enumerate}
    \item auto\_indexer.py monitors folders with watchdog
    \item Detects .txt, .md, .pdf, .docx files
    \item document\_processor.py extracts text
    \item Text chunked (chunk\_size: 500, overlap: 100)
    \item sentence-transformers generates embeddings
    \item Stored in LanceDB vector database
\end{enumerate}

\section{Quick Start}

\begin{lstlisting}[caption=Setup and Run]
# Install Python dependencies
pip install -r requirements.txt

# Clear old data
python clear_all_data.py

# Add documents to test_docs/ folder

# Start main application
python enhanced_global_monitor.py

# OR start Electron app
cd electron-app
npm install
npm start
\end{lstlisting}

\section{Configuration}

config.json contains:
\begin{itemize}
    \item embedding.model\_name: "sentence-transformers/all-MiniLM-L6-v2"
    \item chunking.chunk\_size: 500, chunk\_overlap: 100
    \item vector\_store.db\_path: "./data/vector\_db"
    \item api.host: "127.0.0.1", port: 8000
    \item processing.supported\_extensions: [".pdf", ".docx", ".md", ".txt"]
\end{itemize}

Monitored folders (auto\_indexer.py):
\begin{itemize}
    \item test\_docs/, data/documents/, documents/
\end{itemize}

\section{API Endpoints}

From api\_service.py:
\begin{itemize}
    \item GET /health - Backend status check
    \item POST /search - Search with query, limit, similarity\_threshold
    \item POST /documents/upload - Upload files for indexing
    \item GET /documents - List indexed documents
    \item POST /readwise/import - Import Readwise markdown
    \item GET /indexer/status - Auto-indexer status
\end{itemize}

Search request example:
\begin{lstlisting}[caption=Search API]
POST /search
{
  "query": "machine learning",
  "limit": 10,
  "similarity_threshold": 0.3
}
\end{lstlisting}

\section{Electron App Features}

\subsection{FloatingApp.js}
React component with tabs:
\begin{itemize}
    \item Search tab: Real-time search interface
    \item Canvas tab: Visual workspace for organizing results
    \item Context tab: Contextual suggestions
\end{itemize}

\subsection{Canvas.js}
Visual workspace features:
\begin{itemize}
    \item Drag and drop search results onto canvas
    \item Pan and zoom with mouse controls
    \item Item positioning and selection
    \item Related suggestions panel
    \item Clear canvas functionality
\end{itemize}

\subsection{Global Monitoring}
enhanced\_global\_monitor.py:
\begin{itemize}
    \item keyboard.on\_press() captures keystrokes
    \item Builds query string character by character
    \item SPACEBAR triggers search reset
    \item Double-click results to copy via pyperclip
\end{itemize}

\section{Build Process}

\subsection{Python Executable}
build\_executable.py uses PyInstaller:
\begin{lstlisting}[caption=Build Command]
python build_executable.py
\end{lstlisting}

Creates standalone executable with:
\begin{itemize}
    \item Hidden imports: uvicorn, fastapi, keyboard, sentence\_transformers
    \item Data files: web/, config.json
    \item Main script: realtime\_search\_app.py
\end{itemize}

\subsection{Electron Build}
\begin{lstlisting}[caption=Electron Build]
cd electron-app
npm run build  # Build React renderer
npm run dist   # Create installer
\end{lstlisting}

\section{Key Implementation Details}

\subsection{Enhanced Global Monitor GUI}
Tkinter application (enhanced\_global\_monitor.py):
\begin{itemize}
    \item Window size: 900x650, always on top
    \item Start Backend button: Launches subprocess for FastAPI server
    \item Start Monitor button: Calls keyboard.on\_press() for global monitoring
    \item Current Search display: Shows typed query in real-time
    \item Results Text widget: Displays search results with copy functionality
    \item Double-click to copy: Uses pyperclip to copy selected text chunks
\end{itemize}

\subsection{Auto Indexer Monitoring}
auto\_indexer.py implementation:
\begin{itemize}
    \item Watchdog Observer monitors file system events
    \item Hash-based change detection (get\_file\_info method)
    \item Supported extensions: .txt, .md, .pdf, .docx, .doc
    \item Indexed files tracking in data/indexed\_files.json
    \item Real-time indexing on file creation/modification
\end{itemize}

\subsection{Vector Search Process}
\begin{enumerate}
    \item User types in any application
    \item keyboard.on\_press captures each keystroke
    \item Query built character by character
    \item HTTP POST to /search endpoint
    \item sentence-transformers generates query embedding
    \item LanceDB performs cosine similarity search
    \item Results filtered by similarity\_threshold (default 0.3)
    \item Results displayed with similarity scores
\end{enumerate}

\section{Summary}

This project provides real-time semantic search across local documents through two main interfaces:

\subsection{Tkinter Application}
enhanced\_global\_monitor.py offers a simple desktop GUI that monitors global keyboard input and displays search results in real-time.

\subsection{Electron Application}
Full-featured React-based desktop app with Canvas workspace, floating windows, and advanced UI components.

\subsection{Core Technology}
\begin{itemize}
    \item LanceDB vector database for similarity search
    \item Sentence Transformers for semantic embeddings
    \item FastAPI backend with automatic file indexing
    \item Global keyboard monitoring for real-time search
    \item Support for PDF, DOCX, TXT, MD document formats
\end{itemize}

\end{document}
