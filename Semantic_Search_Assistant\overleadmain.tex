\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage{geometry}
\usepackage{hyperref}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{enumitem}
\usepackage{longtable}

% Page setup
\geometry{margin=1in}

% Colors
\definecolor{codeblue}{RGB}{0,102,204}
\definecolor{backcolour}{RGB}{245,245,245}

% Code listing style
\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},
    basicstyle=\ttfamily\footnotesize,
    breaklines=true,
    keepspaces=true,
    showspaces=false,
    showstringspaces=false,
    tabsize=2
}
\lstset{style=mystyle}

\title{
    \textbf{Semantic Search Assistant} \\
    Real-time Document Search Desktop Application
}
\author{Project Documentation}
\date{\today}

\begin{document}

\maketitle

\section{Project Overview}

\subsection{Description}
Desktop application for real-time semantic search across local documents. Monitors typing in any text editor and provides instant search results from indexed documents.

\subsection{Core Features}
\begin{itemize}
    \item Global keyboard monitoring (enhanced\_global\_monitor.py)
    \item Real-time search on each letter typed
    \item Automatic file indexing (auto\_indexer.py)
    \item Vector similarity search using LanceDB
    \item Electron desktop interface
    \item Canvas for organizing search results
    \item Readwise highlights import
\end{itemize}

\section{Technology Stack}

\subsection{Python Backend}
\begin{itemize}
    \item FastAPI - REST API server (api\_service.py)
    \item LanceDB - Vector database (database.py)
    \item Sentence Transformers - Embedding model (all-MiniLM-L6-v2)
    \item PyPDF2, PyMuPDF, python-docx - Document processing
    \item Watchdog - File monitoring (auto\_indexer.py)
    \item Keyboard, PyWin32 - Global monitoring
    \item Pyperclip - Clipboard integration
\end{itemize}

\subsection{Electron Frontend}
\begin{itemize}
    \item Electron 27.0.0 - Desktop framework
    \item React 18.2.0 - UI components
    \item Tailwind CSS - Styling
    \item Framer Motion - Animations
    \item Axios - HTTP client
    \item Lucide React - Icons
\end{itemize}

\section{Core Components}

\subsection{Backend Components}
\begin{itemize}
    \item \textbf{main.py}: Main backend class with DocumentProcessor, VectorStore, SearchEngine
    \item \textbf{api\_service.py}: FastAPI server with endpoints for search, upload, health
    \item \textbf{document\_processor.py}: Processes PDF, DOCX, TXT, MD files into chunks
    \item \textbf{database.py}: LanceDB vector store with embedding generation
    \item \textbf{search\_engine.py}: Vector similarity search with ranking factors
    \item \textbf{auto\_indexer.py}: Monitors folders, indexes new files automatically
    \item \textbf{readwise\_importer.py}: Imports Readwise markdown exports
\end{itemize}

\subsection{Frontend Components}
\begin{itemize}
    \item \textbf{FloatingApp.js}: Main search interface with tabs (search, canvas, context)
    \item \textbf{Canvas.js}: Visual workspace for organizing search results
    \item \textbf{ApiService.js}: HTTP client for backend communication
    \item \textbf{main.js}: Electron main process
    \item \textbf{preload.js}: IPC bridge between main and renderer
\end{itemize}

\section{Project Structure}

\begin{lstlisting}[caption=Project Files]
Semantic_Search_Assistant/
├── Main Applications
│   ├── enhanced_global_monitor.py     # Main desktop app
│   ├── start_enhanced_monitor.py      # Python launcher
│   └── clear_all_data.py              # Data cleaner
│
├── Backend
│   ├── main.py                        # Backend main class
│   ├── api_service.py                 # FastAPI server
│   ├── document_processor.py          # Document processing
│   ├── search_engine.py               # Vector search
│   ├── database.py                    # LanceDB vector store
│   ├── auto_indexer.py                # Auto file indexing
│   ├── readwise_importer.py           # Readwise integration
│   └── config.py                      # Configuration
│
├── electron-app/
│   ├── src/main.js                    # Electron main
│   ├── src/preload.js                 # IPC bridge
│   └── src/renderer/
│       ├── src/FloatingApp.js         # Main UI
│       ├── src/components/Canvas.js   # Canvas feature
│       └── src/services/ApiService.js # API client
│
├── test_docs/                         # Documents folder
├── data/vector_db/                    # Vector database
├── config.json                        # Configuration
└── requirements.txt                   # Dependencies
\end{lstlisting}

\section{Installation}

\subsection{Python Setup}
\begin{lstlisting}[caption=Install Dependencies]
pip install -r requirements.txt
\end{lstlisting}

Key packages from requirements.txt:
\begin{itemize}
    \item lancedb>=0.3.0
    \item sentence\_transformers>=2.2.0
    \item fastapi>=0.100.0
    \item PyPDF2>=3.0.0, python-docx>=0.8.11
    \item keyboard>=0.13.5, pyperclip>=1.8.2
    \item watchdog>=3.0.0
\end{itemize}

\subsection{Electron Setup}
\begin{lstlisting}[caption=Setup Electron App]
cd electron-app
npm install
cd src/renderer
npm install
npm run build
\end{lstlisting}

\section{Configuration}

\subsection{config.json}
\begin{lstlisting}[caption=Main Configuration]
{
  "embedding": {
    "model_name": "sentence-transformers/all-MiniLM-L6-v2",
    "device": "cpu",
    "batch_size": 32
  },
  "chunking": {
    "chunk_size": 500,
    "chunk_overlap": 100
  },
  "vector_store": {
    "db_path": "./data/vector_db",
    "table_name": "documents",
    "similarity_threshold": 0.7
  },
  "processing": {
    "supported_extensions": [".pdf", ".docx", ".md", ".txt"]
  },
  "api": {
    "host": "127.0.0.1",
    "port": 8000
  }
}
\end{lstlisting}

\subsection{Monitored Folders}
Auto-indexer monitors these folders (from auto\_indexer.py):
\begin{itemize}
    \item test\_docs/
    \item data/documents/
    \item documents/
\end{itemize}

\section{How to Run}

\subsection{Start Application}
\begin{lstlisting}[caption=Launch Commands]
# Clear old data
python clear_all_data.py

# Start main application
python start_enhanced_monitor.py

# Or start enhanced monitor directly
python enhanced_global_monitor.py
\end{lstlisting}

\subsection{Workflow}
\begin{enumerate}
    \item Add documents to test\_docs/ folder
    \item Click "Start Backend" button (starts FastAPI server on port 8000)
    \item Click "Start Monitor" button (enables global keyboard monitoring)
    \item Type in any text editor (Word, Notepad, VS Code)
    \item See search results in real-time
    \item Double-click results to copy to clipboard
    \item Press SPACEBAR to clear search
\end{enumerate}

\section{Key Features}

\subsection{Global Monitoring}
enhanced\_global\_monitor.py implements:
\begin{itemize}
    \item Keyboard hook using keyboard library
    \item Captures typing in any application
    \item Triggers search on each letter
    \item SPACEBAR clears current search
\end{itemize}

\subsection{Auto Indexing}
auto\_indexer.py provides:
\begin{itemize}
    \item Monitors test\_docs/, documents/, data/documents/ folders
    \item Detects new files with watchdog
    \item Hash-based change detection
    \item Supports .txt, .md, .pdf, .docx files
\end{itemize}

\subsection{Canvas Feature}
Canvas.js implements:
\begin{itemize}
    \item Drag and drop search results
    \item Pan and zoom interface
    \item Visual organization of information
    \item Item connections and relationships
\end{itemize}

\section{API Endpoints}

From api\_service.py:

\subsection{Main Endpoints}
\begin{itemize}
    \item GET /health - Backend status
    \item POST /search - Search documents
    \item POST /documents/upload - Upload files
    \item GET /documents - List documents
    \item POST /readwise/import - Import Readwise
    \item GET /indexer/status - Indexing status
    \item POST /indexer/scan - Manual scan
\end{itemize}

\subsection{Search Request}
\begin{lstlisting}[caption=Search API Call]
POST /search
{
  "query": "machine learning",
  "limit": 50,
  "similarity_threshold": 0.3
}
\end{lstlisting}

\subsection{Search Response}
\begin{lstlisting}[caption=Search Response]
{
  "query": "machine learning",
  "results": [
    {
      "content": "Machine learning algorithms...",
      "similarity_score": 0.85,
      "source_file": "ml_notes.pdf"
    }
  ],
  "total_results": 15
}
\end{lstlisting}

\section{Build \& Deploy}

\subsection{Build Executable}
build\_executable.py creates standalone executable:
\begin{lstlisting}[caption=Build Process]
python build_executable.py
\end{lstlisting}

Uses PyInstaller with these components:
\begin{itemize}
    \item Main app: realtime\_search\_app.py
    \item Hidden imports: uvicorn, fastapi, keyboard, sentence\_transformers
    \item Data files: web/, config.json
\end{itemize}

\subsection{Electron Build}
\begin{lstlisting}[caption=Electron Build]
cd electron-app
npm run build
npm run dist
\end{lstlisting}

\section{Implementation Details}

\subsection{Document Processing Flow}
\begin{enumerate}
    \item auto\_indexer.py monitors folders with watchdog
    \item document\_processor.py extracts text from files
    \item Text chunked with LangChain (chunk\_size: 500, overlap: 100)
    \item database.py generates embeddings with sentence-transformers
    \item Vectors stored in LanceDB
\end{enumerate}

\subsection{Search Process}
\begin{enumerate}
    \item enhanced\_global\_monitor.py captures keystrokes
    \item Query sent to /search endpoint
    \item search\_engine.py performs vector similarity search
    \item Results ranked by similarity score
    \item FloatingApp.js displays results
    \item Double-click copies to clipboard via pyperclip
\end{enumerate}

\subsection{Real-time Monitoring}
enhanced\_global\_monitor.py implements:
\begin{itemize}
    \item keyboard.on\_press() for keystroke capture
    \item Builds query string character by character
    \item SPACEBAR triggers search reset
    \item Results displayed in tkinter GUI
\end{itemize}

\section{File Descriptions}

\subsection{Core Python Files}
\begin{itemize}
    \item \textbf{enhanced\_global\_monitor.py}: Main application with tkinter GUI, keyboard monitoring, search interface
    \item \textbf{main.py}: Backend class integrating DocumentProcessor, VectorStore, SearchEngine, AutoIndexer
    \item \textbf{api\_service.py}: FastAPI server with search, upload, health endpoints
    \item \textbf{database.py}: LanceDB vector store with SentenceTransformer embeddings
    \item \textbf{search\_engine.py}: Vector similarity search with ranking factors
    \item \textbf{auto\_indexer.py}: File monitoring with watchdog, hash-based change detection
    \item \textbf{document\_processor.py}: Text extraction from PDF, DOCX, TXT, MD files
    \item \textbf{readwise\_importer.py}: Markdown import with highlight parsing
\end{itemize}

\subsection{Electron Files}
\begin{itemize}
    \item \textbf{main.js}: Electron main process, window management
    \item \textbf{preload.js}: IPC bridge for secure renderer communication
    \item \textbf{FloatingApp.js}: React main UI with search, canvas, context tabs
    \item \textbf{Canvas.js}: Visual workspace with drag-drop, pan-zoom
    \item \textbf{ApiService.js}: Axios HTTP client for backend API calls
\end{itemize}

\subsection{Configuration Files}
\begin{itemize}
    \item \textbf{config.json}: Embedding model, chunking, vector store, API settings
    \item \textbf{requirements.txt}: Python dependencies (lancedb, fastapi, sentence-transformers, etc.)
    \item \textbf{package.json}: Electron and React dependencies
    \item \textbf{connected\_folders.json}: Monitored folder configuration
\end{itemize}

\end{document}
